package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.PointConstant;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.EngineEvent;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.mongo.VerifyResultDataDao;
import com.youxin.risk.commons.service.engine.EngineEventService;
import com.youxin.risk.commons.utils.JsonGrayFeatureUtil;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.service.impl.ProcessInfoHandler;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import com.youxin.risk.engine.utils.EventUtils;
import com.youxin.risk.engine.vo.GuiYinStrategyResultForRtaVo;
import com.youxin.risk.metrics.MetricsAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


/**
 * <AUTHOR>
 */
@Service(value = "strategyTaskNew")
public class StrategyTaskServiceNew extends StrategyTaskBaseNewService {

    protected static Logger logger = LoggerFactory.getLogger(StrategyTaskServiceNew.class);

    @Autowired
    private EngineEventMongoService engineEventMongoService;
    @Resource
    private EngineEventService engineEventService;
    @Autowired
    private VerifyResultDataDao verifyResultDataDao;
    @Resource(name = "guiYinStrategyResultSender")
    private KafkaSyncSender guiYinStrategyResultSender;
    @Autowired
    private EventService eventService;
    @Resource
    private ProcessInfoHandler processInfoHandler;

    private final static List<String> guiYinEvent= Arrays.asList("registeredLogin","auditLoan");

    private static final String HFQ_LOAN_AMOUNT_RESULT_PATH = "currLineManagement";
    private static final String HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH = "curr_line_management";

    @Override
    public void execute(ProcessContext processContext) {
        long startTime = System.currentTimeMillis();
        Event event = processContext.getEvent();
        // 执行策略
        super.execute(processContext);

        //非镜像
        if (!event.isMirror()) {
            // 执行策略耗时统计埋点
            point(event, PointConstant.verify_review_python, startTime);
            verifyResultDataDao.updateVerifyResultMongo(EventVo.eventVo4Update(event));
            //埋点统计每步耗时
            point(event, PointConstant.verify_step_duration, 0);
        }

        // 添加策略节点各步骤的审核结果
        processInfoHandler.addProcessInfo(event, "W","strategy");

        engineEventMongoService.updateEventToMongo(event);
        this.sendGuiYinStrategyResultForRta(event);

    }

    private void sendGuiYinStrategyResultForRta(Event event) {
        try {
            if(!event.isMirror() && guiYinEvent.contains(event.getEventCode())){
                eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, guiYinStrategyResultSender,
                        GuiYinStrategyResultForRtaVo.build(event).toJsonString());
            }
        }catch (Exception e){
            logger.error("sendGuiYinStrategyResultForRta failed,error:", e);
        }

    }

    @Override
    public boolean isMirrorRun() {
        return true;
    }

    @Override
    public void transferStrategyResult(ProcessContext processContext, Event event, Map<String, Object> verifyResult) {
        // 设置策略输入输出 todo 拆出去？ 职责不单一
        // 设置变量集 并固定不变
        processContext.setCurrentNodeInfo(ImmutableMap.copyOf(event.getVariableMap()),
                verifyResult.get(EventVariableKeyEnum.originResult.name()),
                verifyResult.get("strategyId"));
        String amountType = processContext.getAttribute("amountType");
        if (StringUtils.equals(amountType, "AMOUNT_ASSIGN")) {
            // 进件额度信息另存指定位置
            JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_RESULT_PATH, verifyResult);
            if(JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())){
                // 深拷贝
                JSONObject verifyResultNew = JSON.parseObject(JSON.toJSONString(verifyResult));
                JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH, verifyResultNew);
            }
        } else {
            //合并策略结果
            mergeVerifyResultIfNecessary(processContext, event, verifyResult);
        }
    }

    /**
     * 是否将当前策略执行结果merge到上一步的结果中，默认为覆盖
     *
     * @param processContext processContext
     * @param event          event
     * @param verifyResult   verifyResult
     */
    protected void mergeVerifyResultIfNecessary(ProcessContext processContext, Event event,
                                                Map<String, Object> verifyResult) {
        boolean needMerge = Boolean.parseBoolean(processContext.getAttribute("merge"));
        if (needMerge) {
            logger.info("merge currentVerifyResult to previousVerifyResult,sessionId:{}", event.getSessionId());
            Map<String, Object> originalVerifyResult = event.getVerifyResult();
            Set<String> keys = verifyResult.keySet();
            for (String key : keys) {
                if (originalVerifyResult.containsKey(key)) {
                    logger.warn("exists same key in originalVerifyResult,sessionId:{},key:{}", event.getSessionId(),
                            key);
                    continue;
                }
                originalVerifyResult.put(key, verifyResult.get(key));
            }
        } else {
            event.setVerifyResult(verifyResult);
        }
    }


    private void point(Event event, String point, long start) {
        try {
            EngineEvent engineEvent = engineEventService.getBySessionId(event.getSessionId());
            Map<String, String> tags = Maps.newHashMap();
            tags.put("sourceSystem", event.getSourceSystem());
            tags.put("eventCode", event.getEventCode());
            tags.put("step", event.getParams().get("step") == null ? "NONE" : (String) event.getParams().get("step"));
            if (event.getVerifyResult() != null) {
                tags.put("userLevel", (String) event.getVerifyResult().get("userLevel"));
                tags.put("riskResult", (String) event.getVerifyResult().get("verifyResult"));
            } else {
                tags.put("userLevel", "NONE");
                tags.put("riskResult", "NONE");
            }

            Map<String, Number> values = Maps.newHashMap();
            if (start == 0) {
                values.put("cost", System.currentTimeMillis() - engineEvent.getCreateTime().getTime());
            } else {
                values.put("cost", System.currentTimeMillis() - start);
            }

            MetricsAPI.point(point, tags, values);
            LoggerProxy.info("strategyPoint", logger, "process duration info, eventCode={},point={},tags={}," +
                    "values={}", event.getEventCode(), point, tags, values);
        } catch (Exception e) {
            LoggerProxy.error("strategyPointError", logger, "eventCode={},userKey={},loanKey={}",
                    event.getEventCode(), event.getUserKey(), event.getLoanKey(), e);
        }
    }
}
