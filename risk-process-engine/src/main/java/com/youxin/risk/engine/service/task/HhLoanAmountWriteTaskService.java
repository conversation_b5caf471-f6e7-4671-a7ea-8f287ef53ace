package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.utils.JsonGrayFeatureUtil;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MapUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.clients.RiskDcClient;
import com.youxin.risk.engine.service.SplitFlowService;
import com.youxin.risk.engine.service.task.engine.EngineEventMongoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 额度更新节点 好分期进件、api进件专用额度节点
 */
@Component(value = "hhLoanAmountWriteTaskService")
public class HhLoanAmountWriteTaskService extends StrategyTaskBaseNewService {

    protected static Logger logger = LoggerFactory.getLogger(HhLoanAmountWriteTaskService.class);

    @Resource(name = "redisLock")
    private RedisLock redisLock;
    @Resource
    private RiskDcClient riskDcClient;
    @Autowired
    private EngineEventMongoService engineEventMongoService;
    @Resource
    private SplitFlowService splitFlowService;


    private static final String REDIS_KEY_HH_AMOUNT_DEAL = "VERIFY_USER_AMOUNT_";
    private static final int JOB_THRESHOLD_SECONDS = 30;
    private static final String HFQ_TRANSID_KEYWORD = "transId";
    private static final String HFQ_LOAN_AMOUNT_RESULT_PATH = "currLineManagement";
    private static final String HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH = "curr_line_management";

    @Override
    public void execute(ProcessContext processContext) {
        // 加锁,跑策略,check结果
        Event event = processContext.getEvent();
        String lockKey = REDIS_KEY_HH_AMOUNT_DEAL + event.getUserKey();
        LockResult lr = null;
        try {
            lr = redisLock.tryLock(lockKey, JOB_THRESHOLD_SECONDS);
            if (!lr.isSuccess()) {
                LoggerProxy.info("runAmountStrategyLockFailed", logger, "lockKey={}", lockKey);
                return;
            }

            // 额度策略结果转换获取
            transferStrategyResult(processContext, event, event.getVerifyResult());
            // transId不存在,赋值sessionId
            if (null == event.getParams().get(HFQ_TRANSID_KEYWORD)) {
                event.getParams().put(HFQ_TRANSID_KEYWORD, event.getSessionId());
            }

            Object amountVerifyResult = JSONPath.read(JSON.toJSONString(event.getVerifyResult()), HFQ_LOAN_AMOUNT_RESULT_PATH);
            ProcessNode processNode = splitFlowService.getProcessNode(processContext);
            riskDcClient.updateVerifyAmount(event, processNode, amountVerifyResult);

            LoggerProxy.info("updateAmountSuccess", logger, "userKey={},strategyType={}",
                    event.getUserKey(), event.getString("strategyType"));
            engineEventMongoService.updateEventToMongo(event);
        } finally {
            // 解锁
            if (lr != null && lr.isSuccess()) {
                redisLock.releaseLock(lockKey, lr.getLockId());
            }
        }
    }

    @Override
    public void transferStrategyResult(ProcessContext processContext, Event event, Map<String, Object> verifyResult) {
        JSONObject amountVerifyResult =
                (JSONObject) JSONPath.read(JSON.toJSONString(verifyResult), HFQ_LOAN_AMOUNT_RESULT_PATH);
        // 兼容原有返回格式,返回业务方额度信息只第一层key转驼峰
        Map<String, Object> amountOriVerifyResult = MapUtils.underLineKeyToCamelKeyV2(JSON.parseObject(amountVerifyResult.get
                (EventVariableKeyEnum.originResult.name()).toString()));

        if(JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())) {
            JSONObject amountRawVerifyResult =
                    (JSONObject) JSONPath.read(JSON.toJSONString(verifyResult), HFQ_LOAN_AMOUNT_UNDER_RESULT_PATH);
            JSONObject amountRawOriVerifyResult = JSON.parseObject(
                    amountRawVerifyResult.get(EventVariableKeyEnum.originResult.name()).toString());
            amountOriVerifyResult = JsonKeyFormatUtil.mergeResults(amountOriVerifyResult, amountRawOriVerifyResult);
        }
        // verify额度信息另存指定位置
        JSONPath.set(event.getVerifyResult(), HFQ_LOAN_AMOUNT_RESULT_PATH, amountOriVerifyResult);
    }
}
